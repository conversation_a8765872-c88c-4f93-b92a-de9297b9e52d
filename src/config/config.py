"""
配置管理类
"""

import os
from pathlib import Path
from typing import Optional, Literal, List
from pydantic import BaseModel, Field, validator
from dotenv import load_dotenv


class Config(BaseModel):
    """AI-GDB 配置类"""
    
    # ===== 项目配置 =====
    project_code_dir: Path = Field(..., description="项目代码库目录")
    gdb_path: Path = Field(default=Path("/usr/bin/gdb"), description="GDB可执行文件路径")
    target_executable: Optional[Path] = Field(default=None, description="目标可执行文件路径")
    target_args: Optional[str] = Field(default=None, description="目标程序运行时参数")
    test_script_path: Optional[Path] = Field(default=None, description="测试脚本路径")
    
    # ===== LLM配置 =====
    llm_provider: Literal["openai", "mock"] = Field(default="openai", description="LLM提供商")
    llm_api_key: str = Field(..., description="LLM API密钥")
    llm_base_url: Optional[str] = Field(default=None, description="LLM API基础URL")
    llm_model_name: str = Field(default="gpt-4-turbo-preview", description="LLM模型名称")
    llm_nothink: str = Field(default=" ", description="思考模式")
    llm_max_tokens: int = Field(default=4096, description="LLM最大token数")
    llm_max_input: int = Field(default=50000, description="最大输入长度（字符数）")
    llm_temperature: float = Field(default=0.1, description="LLM温度参数")
    
    # ===== 向量数据库配置 =====
    vector_db_type: Literal["chroma", "faiss"] = Field(default="chroma", description="向量数据库类型")
    vector_db_path: Path = Field(default=Path("./data/vector_db"), description="向量数据库存储路径")
    embedding_model: str = Field(default="all-MiniLM-L6-v2", description="嵌入模型名称")
    
    # ===== 调试配置 =====
    max_debug_rounds: int = Field(default=10, description="最大调试轮次")
    max_debug_steps: int = Field(default=600, description="每轮调试的最大时间")
    breakpoint_timeout: int = Field(default=30, description="断点超时时间(秒)")
    gdb_command_timeout: int = Field(default=30, description="GDB命令执行超时时间(秒)")
    enable_memory_leak_detection: bool = Field(default=True, description="是否启用内存泄露检测")
    enable_signal_capture: bool = Field(default=True, description="是否启用信号捕获")
    
    # ===== 日志配置 =====
    log_level: Literal["DEBUG", "INFO", "WARNING", "ERROR"] = Field(default="INFO", description="日志级别")
    log_file_path: Optional[Path] = Field(default=Path("./logs/ai_gdb.log"), description="日志文件路径")
    enable_console_log: bool = Field(default=True, description="是否启用控制台日志")
    llm_debug: bool = Field(default=False, description="是否启用LLM调试日志（记录GDB执行、测试脚本执行、LLM API请求响应）")
    
    # ===== Web界面配置 =====
    web_port: int = Field(default=9000, description="Web服务器端口")
    web_host: str = Field(default="0.0.0.0", description="Web服务器主机")
    enable_web_ui: bool = Field(default=True, description="是否启用Web界面")
    
    # ===== 高级配置 =====
    knowledge_base_rebuild_interval: int = Field(default=24, description="代码知识库重建间隔(小时)")
    enable_auto_fix: bool = Field(default=False, description="是否启用自动代码修复")
    debug_session_timeout: int = Field(default=60, description="调试会话超时时间(分钟)")

    # ===== 代码过滤配置 =====
    code_filter: Optional[str] = Field(default=None, description="代码过滤规则，支持目录、扩展名、正则表达式，多个规则用逗号分隔")

    # ===== 调试终止配置 =====
    exception_only_termination: bool = Field(default=False, description="是否只有捕获到异常信号才结束调试轮次")
    
    class Config:
        """Pydantic配置"""
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        
    @validator("project_code_dir", "gdb_path", pre=True)
    def validate_paths_exist(cls, v):
        """验证路径是否存在"""
        if v is None:
            return v
        path = Path(v)
        if not path.exists():
            raise ValueError(f"路径不存在: {path}")
        return path
    
    @validator("target_executable", "test_script_path", pre=True)
    def validate_optional_paths(cls, v):
        """验证可选路径"""
        if v is None or v == "":
            return None
        path = Path(v)
        if not path.exists():
            raise ValueError(f"路径不存在: {path}")
        return path
    
    @validator("llm_api_key")
    def validate_api_key(cls, v):
        """验证API密钥"""
        if not v or v == "your_api_key_here":
            raise ValueError("请设置有效的LLM API密钥")
        return v
    
    @validator("llm_temperature")
    def validate_temperature(cls, v):
        """验证温度参数"""
        if not 0 <= v <= 2:
            raise ValueError("温度参数必须在0-2之间")
        return v
    
    @validator("web_port")
    def validate_port(cls, v):
        """验证端口号"""
        if not 1 <= v <= 65535:
            raise ValueError("端口号必须在1-65535之间")
        return v

    @staticmethod
    def _parse_size_value(value: str) -> int:
        """解析大小值，支持K、M、G单位"""
        if not value:
            return 0

        value = value.strip().upper()

        # 检查单位
        if value.endswith('K'):
            return int(value[:-1]) * 1000
        elif value.endswith('M'):
            return int(value[:-1]) * 1000000
        elif value.endswith('G'):
            return int(value[:-1]) * 1000000000
        else:
            # 没有单位，直接返回数字
            return int(value)

    @classmethod
    def from_env(cls, env_file: str = ".env") -> "Config":
        """从环境文件加载配置"""
        if os.path.exists(env_file):
            load_dotenv(env_file)
        
        # 从环境变量构建配置
        config_data = {}
        
        # 项目配置
        if project_dir := os.getenv("PROJECT_CODE_DIR"):
            config_data["project_code_dir"] = project_dir
        if gdb_path := os.getenv("GDB_PATH"):
            config_data["gdb_path"] = gdb_path
        if target_exec := os.getenv("TARGET_EXECUTABLE"):
            config_data["target_executable"] = target_exec
        if target_args := os.getenv("TARGET_ARGS"):
            config_data["target_args"] = target_args
        if test_script := os.getenv("TEST_SCRIPT_PATH"):
            config_data["test_script_path"] = test_script
            
        # LLM配置
        if llm_provider := os.getenv("LLM_PROVIDER"):
            config_data["llm_provider"] = llm_provider
        if llm_api_key := os.getenv("LLM_API_KEY"):
            config_data["llm_api_key"] = llm_api_key
        if llm_base_url := os.getenv("LLM_BASE_URL"):
            config_data["llm_base_url"] = llm_base_url
        if llm_model := os.getenv("LLM_MODEL_NAME"):
            config_data["llm_model_name"] = llm_model
        if llm_nothink := os.getenv("LLM_NO_THINK"):
            config_data["llm_nothink"] = llm_nothink
        if llm_max_tokens := os.getenv("LLM_MAX_TOKENS"):
            config_data["llm_max_tokens"] = int(llm_max_tokens)
        if llm_max_input := os.getenv("LLM_MAX_INPUT"):
            config_data["llm_max_input"] = cls._parse_size_value(llm_max_input)
        if llm_temp := os.getenv("LLM_TEMPERATURE"):
            config_data["llm_temperature"] = float(llm_temp)
            
        # 向量数据库配置
        if vector_db_type := os.getenv("VECTOR_DB_TYPE"):
            config_data["vector_db_type"] = vector_db_type
        if vector_db_path := os.getenv("VECTOR_DB_PATH"):
            config_data["vector_db_path"] = vector_db_path
        if embedding_model := os.getenv("EMBEDDING_MODEL"):
            config_data["embedding_model"] = embedding_model
            
        # 调试配置
        if max_rounds := os.getenv("MAX_DEBUG_ROUNDS"):
            config_data["max_debug_rounds"] = int(max_rounds)
        if max_steps := os.getenv("MAX_DEBUG_STEPS"):
            config_data["max_debug_steps"] = int(max_steps)
        if bp_timeout := os.getenv("BREAKPOINT_TIMEOUT"):
            config_data["breakpoint_timeout"] = int(bp_timeout)
        if gdb_timeout := os.getenv("GDB_COMMAND_TIMEOUT"):
            config_data["gdb_command_timeout"] = int(gdb_timeout)
        if mem_leak := os.getenv("ENABLE_MEMORY_LEAK_DETECTION"):
            config_data["enable_memory_leak_detection"] = mem_leak.lower() == "true"
        if signal_cap := os.getenv("ENABLE_SIGNAL_CAPTURE"):
            config_data["enable_signal_capture"] = signal_cap.lower() == "true"
            
        # 日志配置
        if log_level := os.getenv("LOG_LEVEL"):
            config_data["log_level"] = log_level
        if log_file := os.getenv("LOG_FILE_PATH"):
            config_data["log_file_path"] = log_file
        if console_log := os.getenv("ENABLE_CONSOLE_LOG"):
            config_data["enable_console_log"] = console_log.lower() == "true"
            
        # Web界面配置
        if web_port := os.getenv("WEB_PORT"):
            config_data["web_port"] = int(web_port)
        if web_host := os.getenv("WEB_HOST"):
            config_data["web_host"] = web_host
        if web_ui := os.getenv("ENABLE_WEB_UI"):
            config_data["enable_web_ui"] = web_ui.lower() == "true"
            
        # 高级配置
        if kb_interval := os.getenv("KNOWLEDGE_BASE_REBUILD_INTERVAL"):
            config_data["knowledge_base_rebuild_interval"] = int(kb_interval)
        if auto_fix := os.getenv("ENABLE_AUTO_FIX"):
            config_data["enable_auto_fix"] = auto_fix.lower() == "true"
        if session_timeout := os.getenv("DEBUG_SESSION_TIMEOUT"):
            config_data["debug_session_timeout"] = int(session_timeout)
        if llm_debug := os.getenv("LLM_DEBUG"):
            config_data["llm_debug"] = llm_debug.lower() == "true"

        # 代码过滤配置
        if code_filter := os.getenv("CODE_FILTER"):
            config_data["code_filter"] = code_filter

        # 调试终止配置
        if exception_only := os.getenv("EXCEPTION_ONLY_TERMINATION"):
            config_data["exception_only_termination"] = exception_only.lower() == "true"

        return cls(**config_data)
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return self.dict()
    
    def save_to_file(self, file_path: str) -> None:
        """保存配置到文件"""
        config_lines = []
        config_lines.append("# AI-GDB 配置文件")
        config_lines.append("")
        
        config_lines.append("# ===== 项目配置 =====")
        config_lines.append(f"PROJECT_CODE_DIR={self.project_code_dir}")
        config_lines.append(f"GDB_PATH={self.gdb_path}")
        if self.target_executable:
            config_lines.append(f"TARGET_EXECUTABLE={self.target_executable}")
        if self.test_script_path:
            config_lines.append(f"TEST_SCRIPT_PATH={self.test_script_path}")
        config_lines.append("")
        
        config_lines.append("# ===== LLM配置 =====")
        config_lines.append(f"LLM_PROVIDER={self.llm_provider}")
        config_lines.append(f"LLM_API_KEY={self.llm_api_key}")
        if self.llm_base_url:
            config_lines.append(f"LLM_BASE_URL={self.llm_base_url}")
        config_lines.append(f"LLM_MODEL_NAME={self.llm_model_name}")
        config_lines.append(f"LLM_NO_THINK={self.llm_nothink}")
        config_lines.append(f"LLM_MAX_TOKENS={self.llm_max_tokens}")
        config_lines.append(f"LLM_TEMPERATURE={self.llm_temperature}")
        config_lines.append("")
        
        # 继续添加其他配置...
        
        with open(file_path, "w", encoding="utf-8") as f:
            f.write("\n".join(config_lines))

    def BREAKPOINT_BEFORE_RUN(self) -> bool:
        return os.getenv("BREAKPOINT_BEFORE_RUN", "false").lower() == "true"