/**
 * WebSocket 连接管理器
 * 处理与后端的实时通信
 */

class WebSocketManager {
    constructor() {
        this.ws = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 10; // 增加重连次数
        this.reconnectDelay = 1000; // 1秒
        this.isConnecting = false;
        this.messageHandlers = new Map();
        this.statusIndicator = document.getElementById('status-indicator');

        // 心跳相关
        this.pingInterval = 30000; // 30秒
        this.pongTimeout = 10000;  // 10秒
        this.pingTimer = null;
        this.pongTimer = null;
        this.lastPingTime = null;

        // 连接监控
        this.connectionStartTime = null;
        this.lastMessageTime = null;
        this.messageQueue = []; // 离线消息队列

        // 绑定事件处理器
        this.onOpen = this.onOpen.bind(this);
        this.onMessage = this.onMessage.bind(this);
        this.onClose = this.onClose.bind(this);
        this.onError = this.onError.bind(this);

        // 页面可见性监控
        this.setupVisibilityMonitoring();

        // 启动连接监控
        this.startConnectionMonitoring();

        // 自动连接
        this.connect();
    }
    
    /**
     * 建立WebSocket连接
     */
    connect() {
        if (this.isConnecting || (this.ws && this.ws.readyState === WebSocket.OPEN)) {
            return;
        }
        
        this.isConnecting = true;
        this.updateStatus('connecting', '连接中...');
        
        try {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws`;
            
            this.ws = new WebSocket(wsUrl);
            this.ws.onopen = this.onOpen;
            this.ws.onmessage = this.onMessage;
            this.ws.onclose = this.onClose;
            this.ws.onerror = this.onError;
            
        } catch (error) {
            console.error('WebSocket连接失败:', error);
            this.isConnecting = false;
            this.updateStatus('error', '连接失败');
            this.scheduleReconnect();
        }
    }
    
    /**
     * 设置页面可见性监控
     */
    setupVisibilityMonitoring() {
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                console.log('页面进入后台，暂停心跳');
                this.stopHeartbeat();
            } else {
                console.log('页面回到前台，恢复心跳');
                if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                    this.startHeartbeat();
                }
            }
        });
    }

    /**
     * 连接成功处理
     */
    onOpen(event) {
        console.log('WebSocket连接已建立');
        this.isConnecting = false;
        this.reconnectAttempts = 0;
        this.connectionStartTime = Date.now();
        this.lastMessageTime = Date.now();
        this.updateStatus('connected', '已连接');

        // 启动心跳
        this.startHeartbeat();

        // 发送队列中的消息
        this.flushMessageQueue();

        // 触发连接成功事件
        this.emit('connected', event);
    }
    
    /**
     * 消息接收处理
     */
    onMessage(event) {
        try {
            const data = JSON.parse(event.data);
            this.lastMessageTime = Date.now();

            // 处理心跳消息
            if (data.type === 'ping') {
                this.handlePing(data);
                return;
            } else if (data.type === 'pong') {
                this.handlePong(data);
                return;
            }

            //console.log('收到WebSocket消息:', data);

            // 根据消息类型分发处理
            if (data.type && this.messageHandlers.has(data.type)) {
                const handlers = this.messageHandlers.get(data.type);
                handlers.forEach(handler => {
                    try {
                        handler(data);
                    } catch (error) {
                        console.error(`消息处理器错误 (${data.type}):`, error);
                    }
                });
            }

            // 触发通用消息事件
            this.emit('message', data);

        } catch (error) {
            console.error('WebSocket消息解析失败:', error);
        }
    }
    
    /**
     * 处理ping消息
     */
    handlePing(data) {
        // 响应pong消息
        this.sendPong(data.timestamp);
    }

    /**
     * 处理pong消息
     */
    handlePong(data) {
        if (this.pongTimer) {
            clearTimeout(this.pongTimer);
            this.pongTimer = null;
        }

        const latency = Date.now() - this.lastPingTime;
        console.log(`收到pong响应，延迟: ${latency}ms`);
    }

    /**
     * 启动心跳
     */
    startHeartbeat() {
        this.stopHeartbeat(); // 先停止现有的心跳

        if (!document.hidden) { // 只在页面可见时启动心跳
            this.pingTimer = setInterval(() => {
                this.sendPing();
            }, this.pingInterval);
        }
    }

    /**
     * 停止心跳
     */
    stopHeartbeat() {
        if (this.pingTimer) {
            clearInterval(this.pingTimer);
            this.pingTimer = null;
        }
        if (this.pongTimer) {
            clearTimeout(this.pongTimer);
            this.pongTimer = null;
        }
    }

    /**
     * 发送ping消息
     */
    sendPing() {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            this.lastPingTime = Date.now();
            this.send('ping', { timestamp: this.lastPingTime });

            // 设置pong超时
            // this.pongTimer = setTimeout(() => {
            //     console.warn('心跳超时，连接可能已断开');
            //     this.ws.close(1000, '心跳超时');
            // }, this.pongTimeout);
        }
    }

    /**
     * 发送pong消息
     */
    sendPong(pingTimestamp) {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            this.send('pong', {
                timestamp: Date.now(),
                pingTimestamp: pingTimestamp
            });
        }
    }

    /**
     * 发送队列中的消息
     */
    flushMessageQueue() {
        while (this.messageQueue.length > 0) {
            const message = this.messageQueue.shift();
            this.send(message.type, message.data);
        }
    }

    /**
     * 连接关闭处理
     */
    onClose(event) {
        console.log('WebSocket连接已关闭:', event.code, event.reason);
        this.isConnecting = false;
        this.stopHeartbeat(); // 停止心跳
        this.updateStatus('disconnected', '连接断开');

        // 触发断开连接事件
        this.emit('disconnected', event);

        // 如果不是正常关闭，尝试重连
        if (event.code !== 1000) {
            this.scheduleReconnect();
        }
    }
    
    /**
     * 连接错误处理
     */
    onError(event) {
        console.error('WebSocket连接错误:', event);
        this.isConnecting = false;
        this.stopHeartbeat(); // 停止心跳

        // 记录错误详情
        const errorInfo = {
            timestamp: Date.now(),
            event: event,
            readyState: this.ws ? this.ws.readyState : 'unknown',
            connectionDuration: this.connectionStartTime ? Date.now() - this.connectionStartTime : 0
        };

        console.log('连接错误详情:', errorInfo);
        this.updateStatus('error', '连接错误');

        // 触发错误事件
        this.emit('error', errorInfo);
    }
    
    /**
     * 发送消息
     */
    send(type, data = {}, queueIfOffline = true) {
        if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
            if (queueIfOffline && type !== 'ping' && type !== 'pong') {
                // 将非心跳消息加入队列
                this.messageQueue.push({ type, data });
                console.warn('WebSocket未连接，消息已加入队列');
                return false;
            } else {
                console.warn('WebSocket未连接，无法发送消息');
                return false;
            }
        }

        try {
            const message = {
                type: type,
                id: this.generateMessageId(),
                data: data,
                timestamp: Date.now()
            };

            this.ws.send(JSON.stringify(message));
            if (type !== 'ping' && type !== 'pong') {
                console.log('发送WebSocket消息:', message);
            }
            return true;

        } catch (error) {
            console.error('发送WebSocket消息失败:', error);

            // 如果发送失败且不是心跳消息，加入队列
            if (queueIfOffline && type !== 'ping' && type !== 'pong') {
                this.messageQueue.push({ type, data });
            }
            return false;
        }
    }

    /**
     * 生成消息ID
     */
    generateMessageId() {
        return 'msg_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
    
    /**
     * 注册消息处理器
     */
    on(type, handler) {
        if (!this.messageHandlers.has(type)) {
            this.messageHandlers.set(type, []);
        }
        this.messageHandlers.get(type).push(handler);
    }
    
    /**
     * 移除消息处理器
     */
    off(type, handler) {
        if (this.messageHandlers.has(type)) {
            const handlers = this.messageHandlers.get(type);
            const index = handlers.indexOf(handler);
            if (index > -1) {
                handlers.splice(index, 1);
            }
        }
    }
    
    /**
     * 触发事件
     */
    emit(type, data) {
        const event = new CustomEvent(`ws_${type}`, { detail: data });
        document.dispatchEvent(event);
    }
    
    /**
     * 更新状态指示器
     */
    updateStatus(status, text) {
        if (!this.statusIndicator) return;
        
        // 移除所有状态类
        this.statusIndicator.classList.remove('connected', 'disconnected', 'error');
        
        // 添加新状态类
        this.statusIndicator.classList.add(status);
        
        // 更新文本
        const icon = this.statusIndicator.querySelector('i');
        const textNode = this.statusIndicator.childNodes[this.statusIndicator.childNodes.length - 1];
        if (textNode && textNode.nodeType === Node.TEXT_NODE) {
            textNode.textContent = ` ${text}`;
        }
    }
    
    /**
     * 安排重连
     */
    scheduleReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.log('达到最大重连次数，停止重连');
            this.updateStatus('error', `连接失败 (${this.maxReconnectAttempts}次重试后)`);

            // 显示手动重连按钮
            this.showManualReconnectButton();
            return;
        }

        this.reconnectAttempts++;
        // 改进的指数退避算法，最大延迟不超过30秒
        const baseDelay = this.reconnectDelay;
        const maxDelay = 30000; // 30秒
        const delay = Math.min(baseDelay * Math.pow(1.5, this.reconnectAttempts - 1), maxDelay);

        console.log(`${delay}ms后尝试第${this.reconnectAttempts}次重连`);
        this.updateStatus('disconnected', `重连中(${this.reconnectAttempts}/${this.maxReconnectAttempts})`);

        setTimeout(() => {
            // 检查页面是否仍然可见
            if (!document.hidden) {
                this.connect();
            } else {
                console.log('页面在后台，延迟重连');
                // 页面在后台时，等待页面回到前台再重连
                const visibilityHandler = () => {
                    if (!document.hidden) {
                        document.removeEventListener('visibilitychange', visibilityHandler);
                        this.connect();
                    }
                };
                document.addEventListener('visibilitychange', visibilityHandler);
            }
        }, delay);
    }

    /**
     * 显示手动重连按钮
     */
    showManualReconnectButton() {
        // 在状态指示器旁边显示重连按钮
        if (this.statusIndicator && !document.getElementById('manual-reconnect-btn')) {
            const button = document.createElement('button');
            button.id = 'manual-reconnect-btn';
            button.className = 'btn btn-sm btn-outline-primary ms-2';
            button.innerHTML = '<i class="fas fa-redo"></i> 重连';
            button.onclick = () => {
                this.manualReconnect();
                button.remove();
            };
            this.statusIndicator.parentNode.appendChild(button);
        }
    }

    /**
     * 手动重连
     */
    manualReconnect() {
        console.log('手动重连...');
        this.reconnectAttempts = 0;
        this.connect();
    }

    /**
     * 获取连接统计信息
     */
    getConnectionStats() {
        const now = Date.now();
        return {
            state: this.getState(),
            connectionDuration: this.connectionStartTime ? now - this.connectionStartTime : 0,
            lastMessageAge: this.lastMessageTime ? now - this.lastMessageTime : null,
            reconnectAttempts: this.reconnectAttempts,
            queuedMessages: this.messageQueue.length,
            lastPingTime: this.lastPingTime,
            isPageVisible: !document.hidden
        };
    }

    /**
     * 诊断连接问题
     */
    diagnoseConnection() {
        const stats = this.getConnectionStats();
        const issues = [];

        if (stats.state !== 'OPEN') {
            issues.push(`连接状态异常: ${stats.state}`);
        }

        if (stats.lastMessageAge && stats.lastMessageAge > 60000) {
            issues.push(`长时间无消息: ${Math.round(stats.lastMessageAge / 1000)}秒`);
        }

        if (stats.reconnectAttempts > 0) {
            issues.push(`已重连${stats.reconnectAttempts}次`);
        }

        if (stats.queuedMessages > 0) {
            issues.push(`有${stats.queuedMessages}条消息待发送`);
        }

        if (!stats.isPageVisible) {
            issues.push('页面在后台运行');
        }

        return {
            stats: stats,
            issues: issues,
            healthy: issues.length === 0 && stats.state === 'OPEN'
        };
    }

    /**
     * 启动连接监控
     */
    startConnectionMonitoring() {
        // 每30秒检查一次连接状态
        setInterval(() => {
            const diagnosis = this.diagnoseConnection();

            if (!diagnosis.healthy) {
                console.warn('连接诊断发现问题:', diagnosis.issues);

                // 如果连接状态异常且不在重连中，尝试重连
                if (diagnosis.stats.state !== 'OPEN' && !this.isConnecting) {
                    console.log('自动触发重连...');
                    this.connect();
                }
            }
        }, 30000);
    }
    
    /**
     * 手动重连
     */
    reconnect() {
        this.reconnectAttempts = 0;
        this.connect();
    }
    
    /**
     * 关闭连接
     */
    close() {
        if (this.ws) {
            this.ws.close(1000, '用户主动关闭');
        }
    }
    
    /**
     * 获取连接状态
     */
    getState() {
        if (!this.ws) return 'CLOSED';
        
        switch (this.ws.readyState) {
            case WebSocket.CONNECTING:
                return 'CONNECTING';
            case WebSocket.OPEN:
                return 'OPEN';
            case WebSocket.CLOSING:
                return 'CLOSING';
            case WebSocket.CLOSED:
                return 'CLOSED';
            default:
                return 'UNKNOWN';
        }
    }
}

// 全局WebSocket管理器实例
let wsManager = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    wsManager = new WebSocketManager();
    
    // 将实例暴露到全局作用域
    window.wsManager = wsManager;
});

// 页面卸载时关闭连接
window.addEventListener('beforeunload', function() {
    if (wsManager) {
        wsManager.close();
    }
});
