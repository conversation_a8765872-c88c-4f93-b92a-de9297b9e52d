"""
GDB控制器主模块
"""

import asyncio
import signal
import os
from typing import Dict, List, Any, Optional, Callable
from pathlib import Path
from loguru import logger

from .gdb_session import GDBSession, DebugState, StackFrame, Variable
from .breakpoint_manager import BreakpointManager, Breakpoint

class GDBController:
    """GDB控制器"""
    
    def __init__(self, config):
        self.config = config
        self.session: Optional[GDBSession] = None
        self.breakpoint_manager: Optional[BreakpointManager] = None
        self.is_debugging = False
        self.event_handlers: Dict[str, List[Callable]] = {
            'breakpoint_hit': [],
            'program_stopped': [],
            'program_exited': [],
            'signal_received': [],
            'error_occurred': []
        }
        self._debug_session_task: Optional[asyncio.Task] = None
    
    async def start_gdb_session(self, executable_path: Optional[str] = None, broadcaster = None) -> bool:
        """启动调试会话"""
        try:
            logger.info("启动GDB调试会话")
            
            # 创建GDB会话
            self.session = GDBSession(self.config)
            
            # 启动会话
            if not await self.session.start_session(executable_path, broadcaster = broadcaster):
                logger.error("GDB会话启动失败")
                return False
            
            # 创建断点管理器
            self.breakpoint_manager = BreakpointManager(self.session)
            
            self.is_debugging = True
            logger.info("GDB调试会话启动成功")
            
            return True
            
        except Exception as e:
            logger.error(f"启动调试会话失败: {e}")
            return False
    
    async def attach_to_process(self, pid: int) -> bool:
        """附加到进程"""
        if not self.session:
            logger.error("GDB会话未启动")
            return False
        
        try:
            logger.info(f"附加到进程: {pid}")
            
            success = await self.session.attach_process(pid)
            if success:
                logger.info("成功附加到进程")
                await self._trigger_event('program_stopped', await self.session.get_debug_state())
            
            return success
            
        except Exception as e:
            logger.error(f"附加进程失败: {e}")
            return False
    
    async def run_program(self, args: List[str] = None) -> bool:
        """运行程序"""
        if not self.session:
            logger.error("GDB会话未启动")
            return False
        
        try:
            logger.info("运行程序")
            
            success = await self.session.run_program(args)
            # if success:
            #     # 启动调试监控任务
            #     self._debug_session_task = asyncio.create_task(self._monitor_debug_session())
            
            # 清理程序运行时的控制台输出
            # await self.session.clear_console()
            
            return success
            
        except Exception as e:
            logger.error(f"运行程序失败: {e}")
            return False
    
    async def continue_execution(self) -> bool:
        """继续执行"""
        if not self.session:
            logger.error("GDB会话未启动")
            return False
        
        try:
            success = await self.session.continue_execution()
            if success:
                await self._trigger_event('program_stopped', await self.session.get_debug_state())
            
            return success
            
        except Exception as e:
            logger.error(f"继续执行失败: {e}")
            return False
    
    async def step_over(self) -> bool:
        """单步执行（跳过函数调用）"""
        if not self.session:
            logger.error("GDB会话未启动")
            return False
        
        try:
            success = await self.session.step_over()
            if success:
                await self._trigger_event('program_stopped', await self.session.get_debug_state())
            
            return success
            
        except Exception as e:
            logger.error(f"单步执行失败: {e}")
            return False
    
    async def step_into(self) -> bool:
        """单步执行（进入函数调用）"""
        if not self.session:
            logger.error("GDB会话未启动")
            return False
        
        try:
            success = await self.session.step_into()
            if success:
                await self._trigger_event('program_stopped', await self.session.get_debug_state())
            
            return success
            
        except Exception as e:
            logger.error(f"单步执行失败: {e}")
            return False
    
    async def step_out(self) -> bool:
        """跳出当前函数"""
        if not self.session:
            logger.error("GDB会话未启动")
            return False
        
        try:
            success = await self.session.step_out()
            if success:
                await self._trigger_event('program_stopped', await self.session.get_debug_state())
            
            return success
            
        except Exception as e:
            logger.error(f"跳出函数失败: {e}")
            return False

    async def stop_program(self) -> bool:
        """停止程序"""
        if not self.session:
            logger.error("GDB会话未启动")
            return False

        try:
            logger.info("停止程序")

            # 停止当前程序
            kill_out = await self.session.execute_command("kill")
            
            return True

        except Exception as e:
            logger.error(f"重启程序失败: {e}")
            return False
        
    async def restart_program(self) -> bool:
        """重启程序"""
        if not self.session:
            logger.error("GDB会话未启动")
            return False

        try:
            logger.info("重启程序")

            # 停止当前程序
            await self.session.execute_command("kill")

            # 重新运行程序
            args = []
            if self.config.target_args:
                args = self.config.target_args.split()

            success = await self.session.run_program(args)
            if success:
                # 启动调试监控任务
                if self._debug_session_task:
                    self._debug_session_task.cancel()
                #self._debug_session_task = asyncio.create_task(self._monitor_debug_session())

            return success

        except Exception as e:
            logger.error(f"重启程序失败: {e}")
            return False

    async def set_breakpoint(self, location: str, condition: Optional[str] = None) -> Optional[int]:
        """设置断点"""
        if not self.breakpoint_manager:
            logger.error("断点管理器未初始化")
            return None
        
        try:
            bp_id = await self.breakpoint_manager.set_breakpoint(location, condition)
            # if bp_id:
            #     logger.info(f"断点设置成功: {location}")
            
            return bp_id
            
        except Exception as e:
            logger.error(f"设置断点失败: {e}")
            return None
    
    async def remove_breakpoint(self, bp_id: int) -> bool:
        """删除断点"""
        if not self.breakpoint_manager:
            logger.error("断点管理器未初始化")
            return False
        
        try:
            return await self.breakpoint_manager.remove_breakpoint(bp_id)
            
        except Exception as e:
            logger.error(f"删除断点失败: {e}")
            return False
    
    async def get_backtrace(self) -> List[StackFrame]:
        """获取调用栈"""
        if not self.session:
            logger.error("GDB会话未启动")
            return []
        
        try:
            return await self.session.get_backtrace()
            
        except Exception as e:
            logger.error(f"获取调用栈失败: {e}")
            return []
    
    async def get_variables(self, scope: str = "local") -> Dict[str, Variable]:
        """获取变量信息"""
        if not self.session:
            logger.error("GDB会话未启动")
            return {}
        
        try:
            return await self.session.get_variables(scope)
            
        except Exception as e:
            logger.error(f"获取变量信息失败: {e}")
            return {}
    
    async def evaluate_expression(self, expression: str) -> str:
        """计算表达式"""
        if not self.session:
            logger.error("GDB会话未启动")
            return ""
        
        try:
            return await self.session.evaluate_expression(expression)
            
        except Exception as e:
            logger.error(f"计算表达式失败: {e}")
            return ""
    
    async def get_memory_info(self, address: str, size: int = 16) -> str:
        """获取内存信息"""
        if not self.session:
            logger.error("GDB会话未启动")
            return ""
        
        try:
            return await self.session.get_memory_info(address, size)
            
        except Exception as e:
            logger.error(f"获取内存信息失败: {e}")
            return ""
    
    async def execute_gdb_command(self, command: str) -> str:
        """执行原始GDB命令"""
        if not self.session:
            logger.error("GDB会话未启动")
            return "错误：GDB会话未启动"
        
        try:
            return await self.session.execute_command(command)
            
        except Exception as e:
            logger.error(f"执行GDB命令失败: {e}")
            return "执行GDB命令失败: {e}"
    
    async def get_debug_state(self) -> Optional[DebugState]:
        """获取当前调试状态"""
        if not self.session:
            return None
        
        return await self.session.get_debug_state()
    
    async def get_breakpoints(self) -> List[Breakpoint]:
        """获取所有断点"""
        if not self.breakpoint_manager:
            return []
        
        return await self.breakpoint_manager.list_breakpoints()
    
    def add_event_handler(self, event_type: str, handler: Callable):
        """添加事件处理器"""
        if event_type in self.event_handlers:
            self.event_handlers[event_type].append(handler)
        else:
            logger.warning(f"未知事件类型: {event_type}")
    
    def remove_event_handler(self, event_type: str, handler: Callable):
        """移除事件处理器"""
        if event_type in self.event_handlers:
            try:
                self.event_handlers[event_type].remove(handler)
            except ValueError:
                pass
    
    async def _trigger_event(self, event_type: str, data: Any = None):
        """触发事件"""
        if event_type in self.event_handlers:
            for handler in self.event_handlers[event_type]:
                try:
                    if asyncio.iscoroutinefunction(handler):
                        await handler(data)
                    else:
                        handler(data)
                except Exception as e:
                    logger.error(f"事件处理器执行失败: {e}")
    
    async def _monitor_debug_session(self):
        """监控调试会话"""
        try:
            while self.is_debugging and self.session:
                # 检查程序状态
                debug_state = await self.session.get_debug_state()
                
                if debug_state.is_stopped:
                    # 程序停止，检查停止原因
                    if "breakpoint" in debug_state.stop_reason.lower():
                        await self._trigger_event('breakpoint_hit', debug_state)
                    elif "signal" in debug_state.stop_reason.lower():
                        await self._trigger_event('signal_received', debug_state)
                    else:
                        await self._trigger_event('program_stopped', debug_state)
                
                elif not debug_state.is_running:
                    # 程序退出
                    await self._trigger_event('program_exited', debug_state)
                    break
                
                # 短暂休眠
                await asyncio.sleep(0.1)
                
        except Exception as e:
            logger.error(f"调试会话监控失败: {e}")
            await self._trigger_event('error_occurred', str(e))
    
    async def stop_debug_session(self):
        """停止调试会话"""
        try:
            logger.info("停止GDB调试会话")
            
            self.is_debugging = False
            
            # 取消监控任务
            if self._debug_session_task:
                self._debug_session_task.cancel()
                try:
                    await self._debug_session_task
                except asyncio.CancelledError:
                    pass
            
            # 关闭GDB会话
            if self.session:
                await self.session.close_session()
                self.session = None
            
            # 清理断点管理器
            self.breakpoint_manager = None
            
            logger.info("GDB调试会话已停止")
            
        except Exception as e:
            logger.error(f"停止调试会话失败: {e}")
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.stop_debug_session()
    
    async def collect_debug_context(self, debug_state:Optional[DebugState]) -> Dict[str, Any]:
        """收集调试上下文信息"""
        try:
            context = {
                "debug_state": debug_state.__dict__ if debug_state else {},
                "stack_frames": [],
                "local_variables": {},
                "memory_info": {}
            }

            # 获取调用栈
            if debug_state and debug_state.is_stopped:
                context["stack_frames"] = await self.get_backtrace()
                context["local_variables"] = await self.get_variables("local")

                # 如果有当前帧，获取内存信息
                if debug_state.current_frame:
                    try:
                        # 获取一些关键内存地址的信息
                        if debug_state.current_frame.address:
                            context["memory_info"]["current_frame"] = await self.get_memory_info(
                                debug_state.current_frame.address
                            )
                    except Exception as e:
                        logger.debug(f"获取内存信息失败: {e}")

            return context

        except Exception as e:
            logger.error(f"收集调试上下文失败: {e}")
            return {"error": str(e)}