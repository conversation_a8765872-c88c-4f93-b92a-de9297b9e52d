"""
工具管理器
"""

import json
import os
from pathlib import Path
from typing import Dict, List, Any, Optional, Callable, Awaitable
from dataclasses import dataclass
from loguru import logger
from ..gdb_controller.gdb_controller import GDBController
#from ..knowledge_base.knowledge_base import KnowledgeBase


@dataclass
class Tool:
    """工具定义"""
    name: str
    description: str
    parameters: Dict[str, Any]
    function: Callable[..., Awaitable[Any]]
    category: str = "general"


@dataclass
class ToolCall:
    """工具调用"""
    tool_name: str
    arguments: Dict[str, Any]
    call_id: Optional[str] = None


@dataclass
class ToolResult:
    """工具执行结果"""
    success: bool
    result: Any
    error: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


class ToolManager:
    """工具管理器"""
    
    def __init__(self, gdb_controller:GDBController, knowledge_base):
        self.gdb_controller = gdb_controller
        self.knowledge_base = knowledge_base
        self.tools: Dict[str, Tool] = {}
        self._register_tools()
    
    def _register_tools(self):
        """注册所有工具"""
        # GDB控制工具
        self._register_gdb_tool()

        # 代码搜索工具
        self._register_search_tools()

        # 分析工具
        self._register_analysis_tools()

        # 文件操作工具
        self._register_file_tools()
    
    def _register_gdb_tool(self):
        """注册一个GDB执行命令的工具"""
        if not self.gdb_controller:
            return
        
        self.register_tool(Tool(
            name="execute_gdb_command",
            description="执行任意合法、完整的GDB调试命令，如：breakpoint redis_server.cpp:110 , backtrace, info locals等 ",
            parameters={
                "type": "object",
                "properties": {
                    "gdb_command": {
                        "type": "string",
                        "description": "GDB调试命令"
                    }
                },
                "required": ["gdb_command"]
            },
            function=self._execute_gdb_command,
            category="gdb"
        ))
    def _register_gdb_tools(self):
        """注册GDB控制工具"""
        if not self.gdb_controller:
            return
        # 设置断点
        self.register_tool(Tool(
            name="set_breakpoint",
            description="在指定位置设置断点",
            parameters={
                "type": "object",
                "properties": {
                    "location": {
                        "type": "string",
                        "description": "断点位置，可以是函数名、文件:行号或地址"
                    },
                    "condition": {
                        "type": "string",
                        "description": "断点条件（可选）"
                    }
                },
                "required": ["location"]
            },
            function=self._set_breakpoint,
            category="gdb"
        ))
        
        # 删除断点
        self.register_tool(Tool(
            name="remove_breakpoint",
            description="删除指定断点",
            parameters={
                "type": "object",
                "properties": {
                    "breakpoint_id": {
                        "type": "integer",
                        "description": "断点ID"
                    }
                },
                "required": ["breakpoint_id"]
            },
            function=self._remove_breakpoint,
            category="gdb"
        ))
        
        # 继续执行
        self.register_tool(Tool(
            name="continue_execution",
            description="继续程序执行",
            parameters={
                "type": "object",
                "properties": {}
            },
            function=self._continue_execution,
            category="gdb"
        ))
        
        # 单步执行（跳过函数）
        self.register_tool(Tool(
            name="step_over",
            description="单步执行，跳过函数调用",
            parameters={
                "type": "object",
                "properties": {}
            },
            function=self._step_over,
            category="gdb"
        ))
        
        # 单步执行（进入函数）
        self.register_tool(Tool(
            name="step_into",
            description="单步执行，进入函数调用",
            parameters={
                "type": "object",
                "properties": {}
            },
            function=self._step_into,
            category="gdb"
        ))
        
        # 跳出函数
        self.register_tool(Tool(
            name="step_out",
            description="跳出当前函数",
            parameters={
                "type": "object",
                "properties": {}
            },
            function=self._step_out,
            category="gdb"
        ))
        
        # 获取调用栈
        self.register_tool(Tool(
            name="get_backtrace",
            description="获取程序调用栈",
            parameters={
                "type": "object",
                "properties": {}
            },
            function=self._get_backtrace,
            category="gdb"
        ))
        
        # 获取变量
        self.register_tool(Tool(
            name="get_variables",
            description="获取变量信息",
            parameters={
                "type": "object",
                "properties": {
                    "scope": {
                        "type": "string",
                        "description": "变量作用域：local（局部）、args（参数）",
                        "default": "local"
                    }
                }
            },
            function=self._get_variables,
            category="gdb"
        ))
        
        # 计算表达式
        self.register_tool(Tool(
            name="evaluate_expression",
            description="计算表达式的值",
            parameters={
                "type": "object",
                "properties": {
                    "expression": {
                        "type": "string",
                        "description": "要计算的表达式"
                    }
                },
                "required": ["expression"]
            },
            function=self._evaluate_expression,
            category="gdb"
        ))
        
        # 获取内存信息
        self.register_tool(Tool(
            name="get_memory_info",
            description="获取指定地址的内存信息",
            parameters={
                "type": "object",
                "properties": {
                    "address": {
                        "type": "string",
                        "description": "内存地址"
                    },
                    "size": {
                        "type": "integer",
                        "description": "读取大小（字节数）",
                        "default": 16
                    }
                },
                "required": ["address"]
            },
            function=self._get_memory_info,
            category="gdb"
        ))
    
    def _register_search_tools(self):
        """注册代码搜索工具"""
        if not self.knowledge_base:
            return
        # 搜索代码
        self.register_tool(Tool(
            name="search_code",
            description="在代码库中搜索相关代码",
            parameters={
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "搜索查询"
                    },
                    "top_k": {
                        "type": "integer",
                        "description": "返回结果数量",
                        "default": 5
                    }
                },
                "required": ["query"]
            },
            function=self._search_code,
            category="search"
        ))
        
        # 查找符号
        self.register_tool(Tool(
            name="find_symbol",
            description="查找指定符号（函数、变量等）",
            parameters={
                "type": "object",
                "properties": {
                    "symbol_name": {
                        "type": "string",
                        "description": "符号名称"
                    }
                },
                "required": ["symbol_name"]
            },
            function=self._find_symbol,
            category="search"
        ))
        
        # 获取函数上下文
        self.register_tool(Tool(
            name="get_function_context",
            description="获取函数的完整上下文代码",
            parameters={
                "type": "object",
                "properties": {
                    "function_name": {
                        "type": "string",
                        "description": "函数名称"
                    }
                },
                "required": ["function_name"]
            },
            function=self._get_function_context,
            category="search"
        ))
    
    def _register_analysis_tools(self):
        """注册分析工具"""
        # 分析崩溃上下文
        # self.register_tool(Tool(
        #     name="analyze_crash_context",
        #     description="分析程序崩溃的上下文信息",
        #     parameters={
        #         "type": "object",
        #         "properties": {
        #             "crash_info": {
        #                 "type": "object",
        #                 "description": "崩溃信息，包含function、file、line等字段"
        #             }
        #         },
        #         "required": ["crash_info"]
        #     },
        #     function=self._analyze_crash_context,
        #     category="analysis"
        # ))
        
        # 获取调试建议
        # self.register_tool(Tool(
        #     name="get_debugging_suggestions",
        #     description="基于错误描述获取调试建议",
        #     parameters={
        #         "type": "object",
        #         "properties": {
        #             "error_description": {
        #                 "type": "string",
        #                 "description": "错误描述"
        #             }
        #         },
        #         "required": ["error_description"]
        #     },
        #     function=self._get_debugging_suggestions,
        #     category="analysis"
        # ))

    def _register_file_tools(self):
        """注册文件操作工具"""
        # 读取文件
        self.register_tool(Tool(
            name="read_file",
            description="读取指定文件的内容",
            parameters={
                "type": "object",
                "properties": {
                    "file_path": {
                        "type": "string",
                        "description": "文件路径，可以是相对路径（相对于项目根目录）或绝对路径"
                    },
                    "max_lines": {
                        "type": "integer",
                        "description": "最大读取行数，默认1000行",
                        "default": 1000
                    },
                    "encoding": {
                        "type": "string",
                        "description": "文件编码，默认utf-8",
                        "default": "utf-8"
                    }
                },
                "required": ["file_path"]
            },
            function=self._read_file,
            category="file"
        ))

    def is_step_tool(self, tool_call_data) -> bool:
        tool_name=tool_call_data.get("name", "")
        if tool_name == "execute_gdb_command":
            arguments=tool_call_data.get("arguments", {})
            arg_str = f"{arguments}"
            step_tools = ["step", "step_out", "step_over", "continue", "restart", "kill","run"]
            if any(keyword in arg_str for keyword in step_tools):
                return True
        return False
    
    def is_run_tool(self, tool_call_data) -> bool:
        tool_name=tool_call_data.get("name", "")
        if tool_name == "execute_gdb_command":
            arguments=tool_call_data.get("arguments", {})
            arg_str = f"{arguments}"
            if "run " in arg_str:
                return True
        return False
        
    def register_tool(self, tool: Tool):
        """注册工具"""
        self.tools[tool.name] = tool
        logger.debug(f"注册工具: {tool.name}")
    
    def get_tool(self, name: str) -> Optional[Tool]:
        """获取工具"""
        return self.tools.get(name)
    
    def get_tools_by_category(self, category: str) -> List[Tool]:
        """按类别获取工具"""
        return [tool for tool in self.tools.values() if tool.category == category]
    
    def get_all_tools(self) -> List[Tool]:
        """获取所有工具"""
        return list(self.tools.values())
    
    def get_tools_schema(self) -> List[Dict[str, Any]]:
        """获取工具模式（用于LLM）"""
        schema = []
        for tool in self.tools.values():
            tool_schema = {
                "type": "function",
                "function": {
                    "name": tool.name,
                    "description": tool.description,
                    "parameters": tool.parameters
                }
            }
            schema.append(tool_schema)
        return schema
    
    async def execute_tool(self, tool_call: ToolCall) -> ToolResult:
        """执行工具"""
        try:
            tool = self.get_tool(tool_call.tool_name)
            if not tool:
                return ToolResult(
                    success=False,
                    result=None,
                    error=f"工具不存在: {tool_call.tool_name}"
                )
            
            logger.info(f"执行工具: {tool_call.tool_name} ，参数: {tool_call.arguments}")
            
            # 执行工具函数
            result = await tool.function(**tool_call.arguments)
            
            return ToolResult(
                success=True,
                result=result,
                metadata={"tool_name": tool_call.tool_name}
            )
            
        except Exception as e:
            logger.error(f"工具执行失败: {e}")
            return ToolResult(
                success=False,
                result=None,
                error=str(e),
                metadata={"tool_name": tool_call.tool_name}
            )
    
    # 执行GDB命令
    async def _execute_gdb_command(self, gdb_command: str) -> str:
        """执行任意gdb命令"""
        gdb_out = await self.gdb_controller.execute_gdb_command(gdb_command)
        return gdb_out
    # GDB工具实现
    async def _set_breakpoint(self, location: str, condition: Optional[str] = None) -> Dict[str, Any]:
        """设置断点"""
        bp_id = await self.gdb_controller.set_breakpoint(location, condition)
        return {
            "breakpoint_id": bp_id,
            "location": location,
            "condition": condition,
            "success": bp_id is not None
        }
    
    async def _remove_breakpoint(self, breakpoint_id: int) -> Dict[str, Any]:
        """删除断点"""
        success = await self.gdb_controller.remove_breakpoint(breakpoint_id)
        return {
            "breakpoint_id": breakpoint_id,
            "success": success
        }
    
    async def _continue_execution(self) -> Dict[str, Any]:
        """继续执行"""
        success = await self.gdb_controller.continue_execution()
        debug_state = await self.gdb_controller.get_debug_state()
        return {
            "success": success,
            "debug_state": debug_state.__dict__ if debug_state else None
        }
    
    async def _step_over(self) -> Dict[str, Any]:
        """单步执行（跳过函数）"""
        success = await self.gdb_controller.step_over()
        debug_state = await self.gdb_controller.get_debug_state()
        return {
            "success": success,
            "debug_state": debug_state.__dict__ if debug_state else None
        }
    
    async def _step_into(self) -> Dict[str, Any]:
        """单步执行（进入函数）"""
        success = await self.gdb_controller.step_into()
        debug_state = await self.gdb_controller.get_debug_state()
        return {
            "success": success,
            "debug_state": debug_state.__dict__ if debug_state else None
        }
    
    async def _step_out(self) -> Dict[str, Any]:
        """跳出函数"""
        success = await self.gdb_controller.step_out()
        debug_state = await self.gdb_controller.get_debug_state()
        return {
            "success": success,
            "debug_state": debug_state.__dict__ if debug_state else None
        }
    
    async def _get_backtrace(self) -> Dict[str, Any]:
        """获取调用栈"""
        frames = await self.gdb_controller.get_backtrace()
        return {
            "frames": [frame.__dict__ for frame in frames]
        }
    
    async def _get_variables(self, scope: str = "local") -> Dict[str, Any]:
        """获取变量"""
        variables = await self.gdb_controller.get_variables(scope)
        return {
            "scope": scope,
            "variables": {name: var.__dict__ for name, var in variables.items()}
        }
    
    async def _evaluate_expression(self, expression: str) -> Dict[str, Any]:
        """计算表达式"""
        result = await self.gdb_controller.evaluate_expression(expression)
        return {
            "expression": expression,
            "result": result
        }
    
    async def _get_memory_info(self, address: str, size: int = 16) -> Dict[str, Any]:
        """获取内存信息"""
        memory_info = await self.gdb_controller.get_memory_info(address, size)
        return {
            "address": address,
            "size": size,
            "memory_info": memory_info
        }
    
    # 搜索工具实现
    async def _search_code(self, query: str, top_k: int = 5) -> Dict[str, Any]:
        """搜索代码"""
        results = self.knowledge_base.search_code(query, top_k)
        return {
            "query": query,
            "results": [
                {
                    "score": result.score,
                    "file_path": result.chunk.file_path,
                    "start_line": result.chunk.start_line,
                    "end_line": result.chunk.end_line,
                    "content": result.chunk.content[:500],  # 限制内容长度
                    "symbols": [s.name for s in result.chunk.symbols]
                }
                for result in results
            ]
        }
    
    async def _find_symbol(self, symbol_name: str) -> Dict[str, Any]:
        """查找符号"""
        symbols = self.knowledge_base.find_symbol(symbol_name)
        return {
            "symbol_name": symbol_name,
            "symbols": [
                {
                    "name": symbol.name,
                    "type": symbol.type,
                    "file_path": symbol.file_path,
                    "line_number": symbol.line_number,
                    "signature": symbol.signature
                }
                for symbol in symbols
            ]
        }
    
    async def _get_function_context(self, function_name: str) -> Dict[str, Any]:
        """获取函数上下文"""
        chunk = self.knowledge_base.get_function_context(function_name)
        if chunk:
            return {
                "function_name": function_name,
                "file_path": chunk.file_path,
                "start_line": chunk.start_line,
                "end_line": chunk.end_line,
                "content": chunk.content,
                "symbols": [s.name for s in chunk.symbols],
                "dependencies": chunk.dependencies
            }
        else:
            return {
                "function_name": function_name,
                "found": False
            }
    
    # 分析工具实现
    async def _analyze_crash_context(self, crash_info: Dict[str, Any]) -> Dict[str, Any]:
        """分析崩溃上下文"""
        context = self.knowledge_base.analyze_crash_context(crash_info)
        return context
    
    async def _get_debugging_suggestions(self, error_description: str) -> Dict[str, Any]:
        """获取调试建议"""
        suggestions = self.knowledge_base.get_debugging_suggestions(error_description)
        return {
            "error_description": error_description,
            "suggestions": suggestions
        }

    # 文件操作工具实现
    async def _read_file(self, file_path: str, max_lines: int = 1000, encoding: str = "utf-8") -> Dict[str, Any]:
        """读取文件内容"""
        try:
            # 将相对路径转换为绝对路径
            path = Path(file_path)
            if not path.is_absolute():
                # 如果是相对路径，相对于项目根目录
                if hasattr(self, 'knowledge_base') and hasattr(self.knowledge_base, 'config'):
                    project_root = self.knowledge_base.config.project_code_dir
                    path = project_root / path
                else:
                    # 回退到当前工作目录
                    path = Path.cwd() / path

            # 检查文件是否存在
            if not path.exists():
                return {
                    "success": False,
                    "error": f"文件不存在: {file_path}",
                    "file_path": str(path)
                }

            # 检查是否是文件（不是目录）
            if not path.is_file():
                return {
                    "success": False,
                    "error": f"路径不是文件: {file_path}",
                    "file_path": str(path)
                }

            # 检查文件大小，避免读取过大的文件
            file_size = path.stat().st_size
            max_size = 10 * 1024 * 1024  # 10MB
            if file_size > max_size:
                return {
                    "success": False,
                    "error": f"文件过大 ({file_size} bytes)，超过限制 ({max_size} bytes)",
                    "file_path": str(path),
                    "file_size": file_size
                }

            # 读取文件内容
            try:
                with open(path, 'r', encoding=encoding, errors='ignore') as f:
                    lines = f.readlines()

                # 限制行数
                if len(lines) > max_lines:
                    content = ''.join(lines[:max_lines])
                    truncated = True
                    total_lines = len(lines)
                else:
                    content = ''.join(lines)
                    truncated = False
                    total_lines = len(lines)

                # 获取文件信息
                file_info = {
                    "size": file_size,
                    "lines": total_lines,
                    "extension": path.suffix,
                    "name": path.name,
                    "parent": str(path.parent)
                }

                return {
                    "success": True,
                    "file_path": str(path),
                    "content": content,
                    "truncated": truncated,
                    "lines_read": min(len(lines), max_lines),
                    "total_lines": total_lines,
                    "file_info": file_info,
                    "encoding": encoding
                }

            except UnicodeDecodeError as e:
                # 尝试其他编码
                for alt_encoding in ['latin-1', 'cp1252', 'iso-8859-1']:
                    try:
                        with open(path, 'r', encoding=alt_encoding, errors='ignore') as f:
                            lines = f.readlines()

                        if len(lines) > max_lines:
                            content = ''.join(lines[:max_lines])
                            truncated = True
                        else:
                            content = ''.join(lines)
                            truncated = False

                        return {
                            "success": True,
                            "file_path": str(path),
                            "content": content,
                            "truncated": truncated,
                            "lines_read": min(len(lines), max_lines),
                            "total_lines": len(lines),
                            "file_info": {
                                "size": file_size,
                                "lines": len(lines),
                                "extension": path.suffix,
                                "name": path.name,
                                "parent": str(path.parent)
                            },
                            "encoding": alt_encoding,
                            "encoding_warning": f"原始编码 {encoding} 失败，使用 {alt_encoding}"
                        }
                    except:
                        continue

                return {
                    "success": False,
                    "error": f"无法解码文件，尝试了多种编码: {e}",
                    "file_path": str(path)
                }

        except PermissionError:
            return {
                "success": False,
                "error": f"没有权限读取文件: {file_path}",
                "file_path": str(path) if 'path' in locals() else file_path
            }
        except Exception as e:
            return {
                "success": False,
                "error": f"读取文件时发生错误: {str(e)}",
                "file_path": str(path) if 'path' in locals() else file_path
            }
