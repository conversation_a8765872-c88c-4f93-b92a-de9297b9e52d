
from typing import Dict, List, Any, Optional
from dotenv import load_dotenv
import os

load_dotenv()
class PromptManager:
    """提示词管理"""

    def __init__(self, config):
        self.config = config
        self.system_prompt_template = os.getenv("LLM_SYSTEM_PROMPT_TEMPLATE", "你是一个专业的C/C++调试专家，精通GDB、内存分析、并发调试等各种调试技术")
        self.debug_prompt_template = os.getenv("LLM_DEBUG_PROMPT_TEMPLATE","")
        self.out_format = """
            请严格按照以下JSON格式输出:
             ```json
            {
                "error_found": "是否确定故障原因：true/false",
                "explanation": "详细解释故障原因, 给出关键证据列表及具体的代码错误",
                "recommendation": "建议的修复方案，给出具体的修复代码（含前后代码上下文）",
                "next_step": "下一步操作：继续本轮调试/重新调试/终止调试/无"
            }
            ```
            """        
        self.out_format2 = """
            请严格按照以下JSON格式输出:
             ```json
            {
                "error_found": "true/false",  // 仅当100%确认时设为true
                "explanation": ["崩溃地址0xFFFF", "ptr=0x0"],  // 关键证据列表
                "root_cause": "具体代码文件及行号（例：src/core.c:158）",
                "fix": "修复代码示例（含前后代码上下文）",
                "next_action": {
                    "command": "GDB命令（如：watch -l var），可以空",
                    "reason": "预期获取的关键信息"
                }
            }
            ```
        """
    def _render_template(self, template: str, variables: Dict[str, Any]) -> str:
        """渲染模板，替换变量"""                
        # 使用format方法进行变量替换
        try:
            rendered = template.format(**variables)
            return rendered
        except KeyError as e:
            raise ValueError(f"模板变量缺失: {e}")
        except Exception as e:
            raise Exception(f"模板渲染失败: {e}")
    def create_system_prompt(self, project_analysis, fault_description, last_summary = "") -> str:
        """创建系统提示"""
        variables = {
            "out_format": self.out_format,
            "fault_description": fault_description,
            "project_analysis": f"{project_analysis}" or "无",
            "last_debug_summary": last_summary or "无",
        }

        return self._render_template(self.system_prompt_template, variables)
    
    def create_initial_analysis_prompt(self, fault_description: str, round_summary: List[str]) -> str:
        """创建初始分析提示(user)"""
        prompt_parts = [
            "根据项目的异常故障和历史调试记录的总结，对本次调试的C/C++项目进行综合分析，并调用工具分析代码，找出关键位置设置断点，断点要求有明确的文件名及行号，文件和行号必须是项目代码中真实存在的。\n",
            ""
        ]
    #     if len(round_summary) > 0:
    #         history_lines = "\\n".join([f"第{i+1}轮: {summary}" for i, summary in enumerate(round_summary)])
    #         history = f"""## 历史调试记录总结：
    # {history_lines}
    # """
    #     else:
    #         history = "## 历史调试记录总结：无"
    #     prompt_parts.append(history)

        return "\n".join(prompt_parts)
    def create_debug_state_prompt(
        self, fault_description: str, debug_state, context_info: Dict[str, Any], round_result: Dict[str, Any]
    ) -> str:
        """创建行动决策提示"""
        variables = {
            "fault_description": fault_description,
            "context_info": context_info or "无"
        }

        return self._render_template(self.debug_prompt_template, variables)
    def create_state_analysis_prompt(self, debug_state) -> str:
        """创建状态分析提示"""
        prompt_parts = [
            "请分析当前的调试状态：",
            ""
        ]
        
        if debug_state:
            prompt_parts.extend([
                f"程序状态: {'运行中' if debug_state.is_running else '已停止'}",
                f"停止原因: {debug_state.stop_reason}",
            ])
            
            if debug_state.current_frame:
                frame = debug_state.current_frame
                prompt_parts.extend([
                    f"当前函数: {frame.function}",
                    f"文件位置: {frame.file}:{frame.line}",
                ])
            
            if debug_state.variables:
                prompt_parts.append("局部变量:")
                for name, var in list(debug_state.variables.items())[:5]:  # 限制显示数量
                    prompt_parts.append(f"  {name} = {var.value}")
        
        prompt_parts.extend([
            "",
            "请回答以下问题：",
            "1. 当前状态是否表明存在问题？",
            "2. 如果存在问题，可能的原因是什么？",
            "3. 问题是否已经解决？",
            "4. 下一步应该采取什么行动？"
        ])
        
        return "\n".join(prompt_parts)

    def _create_round_summary_prompt(
        self, round_result: Dict[str, Any], fault_description: str
    ) -> str:
        """创建轮次总结提示"""
        prompt_parts = [
            f"请总结第 {round_result['round']} 轮调试的结果：",
            f"",
            f"故障描述: {fault_description}",
            f"轮次状态: {'成功' if round_result.get('success', False) else '失败'}",
            f"终止原因: {round_result.get('termination_reason', '未知')}",
            f"执行步数: {round_result.get('step_count', 0)}",
            f"",
        ]

        # 添加错误发现信息
        if round_result.get("error_found"):
            prompt_parts.append(f"✓ 找到错误原因: {round_result.get('error_cause', '未详细说明')}")
            if round_result.get("solution"):
                prompt_parts.append(f"✓ 解决方案: {round_result.get('solution')}")
        elif round_result.get("error_reproduced"):
            prompt_parts.append(f"✓ 成功复现错误，但未找到根本原因")
        else:
            prompt_parts.append(f"✗ 未能复现或定位错误")

        prompt_parts.append(f"")

        # 添加调试步骤信息
        debug_steps = round_result.get("debug_steps", [])
        if debug_steps:
            prompt_parts.append(f"主要调试步骤:")
            for i, step in enumerate(debug_steps[-5:], 1):  # 只显示最后5步
                step_desc = step.get("step", "未知步骤")
                prompt_parts.append(f"  {i}. {step_desc}")

        prompt_parts.extend([
            f"",
            f"请提供总结，包括：",
            f"1. 本轮的主要发现",
            f"2. 关键的调试行动",
            f"3. 遇到的问题",
            f"4. 对下轮调试的建议"
        ])

        return "\n".join(prompt_parts)