"""
代码知识库主模块
"""

import asyncio
from pathlib import Path
from typing import List, Dict, Any, Optional
from loguru import logger

from .code_parser import CodeParser, CodeChunk, CodeSymbol
from .vector_store import create_vector_store, SearchResult
from .project_analyzer import ProjectAnalyzer
from ..ai_agent.llm_client import LLMSession, Message, LLMResponse
from ..ai_agent.tool_manager import ToolManager, ToolCall, ToolResult
from ..config.config import Config


class KnowledgeBase:
    """代码知识库"""
    
    def __init__(self, config:Config):
        self.config = config
        self.code_parser = CodeParser(config)
        self.vector_store = create_vector_store(config)
        self.chunks_cache: List[CodeChunk] = []
        self.symbols_index: Dict[str, List[CodeSymbol]] = {}

        # 尝试加载已有的知识库数据
        self._load_existing_knowledge_base()

    def _load_existing_knowledge_base(self) -> None:
        """加载已有的知识库数据"""
        try:
            logger.info("尝试加载已有的知识库数据...")

            # 从向量存储中获取所有chunks
            all_chunks = self.vector_store.get_all_chunks()

            if all_chunks:
                # 缓存代码块
                self.chunks_cache = all_chunks

                # 重建符号索引
                self._build_symbols_index(all_chunks)

                logger.info(f"成功加载已有知识库，共 {len(all_chunks)} 个代码块，{len(self.symbols_index)} 个符号")
            else:
                logger.info("未找到已有的知识库数据")

        except Exception as e:
            logger.warning(f"加载已有知识库数据失败: {e}")
            # 如果加载失败，保持空的缓存和索引
            self.chunks_cache = []
            self.symbols_index = {}

    async def build_knowledge_base(self) -> None:
        """构建代码知识库"""
        logger.info("开始构建代码知识库...")
        
        try:
            # 解析项目代码
            logger.info("解析项目代码...")
            chunks = await self._parse_project_async()
            
            if not chunks:
                logger.warning("未找到任何代码块")
                return
            
            # 构建符号索引
            logger.info("构建符号索引...")
            self._build_symbols_index(chunks)
            
            # 清空现有向量存储
            logger.info("清空现有向量存储...")
            self.vector_store.delete_all()
            
            # 添加代码块到向量存储
            logger.info("添加代码块到向量存储...")
            self.vector_store.add_chunks(chunks)
            
            # 缓存代码块
            self.chunks_cache = chunks

            logger.info(f"代码知识库构建完成，共处理 {len(chunks)} 个代码块")

            # 构建完成后进行项目分析
            await self.analyze_project_with_llm()

        except Exception as e:
            logger.error(f"构建代码知识库失败: {e}")
            raise    
    async def _parse_project_async(self) -> List[CodeChunk]:
        """异步解析项目"""
        # 在线程池中运行解析任务
        loop = asyncio.get_event_loop()
        chunks = await loop.run_in_executor(
            None, 
            self.code_parser.parse_project, 
            self.config.project_code_dir
        )
        return chunks
    
    def _build_symbols_index(self, chunks: List[CodeChunk]) -> None:
        """构建符号索引"""
        self.symbols_index.clear()
        
        for chunk in chunks:
            for symbol in chunk.symbols:
                if symbol.name not in self.symbols_index:
                    self.symbols_index[symbol.name] = []
                self.symbols_index[symbol.name].append(symbol)
        
        logger.info(f"符号索引构建完成，共 {len(self.symbols_index)} 个符号")
    
    def search_code(self, query: str, top_k: int = 5) -> List[SearchResult]:
        """搜索代码"""
        logger.debug(f"搜索代码: {query}")
        
        try:
            results = self.vector_store.search(query, top_k)
            logger.debug(f"搜索返回 {len(results)} 个结果")
            return results
        except Exception as e:
            logger.error(f"搜索代码失败: {e}")
            return []
    
    def search_symbol(self, symbol_name: str) -> List[CodeSymbol]:
        """查找符号"""
        return self.vector_store.search(symbol_name, 5)
    def find_symbol(self, symbol_name: str) -> List[CodeSymbol]:
        """查找符号"""
        return self.symbols_index.get(symbol_name, [])
    
    def find_symbols_by_type(self, symbol_type: str) -> List[CodeSymbol]:
        """按类型查找符号"""
        symbols = []
        for symbol_list in self.symbols_index.values():
            for symbol in symbol_list:
                if symbol.type == symbol_type:
                    symbols.append(symbol)
        return symbols
    
    def get_function_context(self, function_name: str) -> Optional[CodeChunk]:
        """获取函数上下文"""
        symbols = self.find_symbol(function_name)
        
        for symbol in symbols:
            if symbol.type == 'function':
                # 查找包含该符号的代码块
                for chunk in self.chunks_cache:
                    if any(s.name == function_name and s.type == 'function' for s in chunk.symbols):
                        return chunk
        
        return None
    
    def get_file_context(self, file_path: str) -> List[CodeChunk]:
        """获取文件上下文"""
        file_chunks = []
        
        for chunk in self.chunks_cache:
            if chunk.file_path == file_path or chunk.file_path.endswith(file_path):
                file_chunks.append(chunk)
        
        return file_chunks
    
    def get_related_code(self, symbol_name: str, max_depth: int = 2) -> List[CodeChunk]:
        """获取相关代码（基于依赖关系）"""
        related_chunks = []
        visited_symbols = set()
        
        def _find_related_recursive(symbol: str, depth: int):
            if depth > max_depth or symbol in visited_symbols:
                return
            
            visited_symbols.add(symbol)
            
            # 查找包含该符号的代码块
            for chunk in self.chunks_cache:
                if any(s.name == symbol for s in chunk.symbols):
                    if chunk not in related_chunks:
                        related_chunks.append(chunk)
                    
                    # 递归查找依赖
                    for dep in chunk.dependencies:
                        _find_related_recursive(dep, depth + 1)
                    
                    # 递归查找符号
                    for chunk_symbol in chunk.symbols:
                        _find_related_recursive(chunk_symbol.name, depth + 1)
        
        _find_related_recursive(symbol_name, 0)
        return related_chunks
    
    def analyze_crash_context(self, crash_info: Dict[str, Any]) -> Dict[str, Any]:
        """分析崩溃上下文"""
        context = {
            "relevant_functions": [],
            "relevant_files": [],
            "potential_causes": [],
            "related_code": []
        }
        
        try:
            # 从崩溃信息中提取函数名和文件名
            function_name = crash_info.get('function')
            file_path = crash_info.get('file')
            line_number = crash_info.get('line')
            
            # 查找相关函数
            if function_name:
                func_symbols = self.find_symbol(function_name)
                context["relevant_functions"] = [
                    {
                        "name": s.name,
                        "file": s.file_path,
                        "line": s.line_number,
                        "signature": s.signature
                    }
                    for s in func_symbols
                ]
                
                # 获取函数上下文
                func_chunk = self.get_function_context(function_name)
                if func_chunk:
                    context["related_code"].append({
                        "type": "function_context",
                        "chunk": func_chunk
                    })
            
            # 查找相关文件
            if file_path:
                file_chunks = self.get_file_context(file_path)
                context["relevant_files"] = [
                    {
                        "path": chunk.file_path,
                        "start_line": chunk.start_line,
                        "end_line": chunk.end_line,
                        "symbols": [s.name for s in chunk.symbols]
                    }
                    for chunk in file_chunks
                ]
                
                context["related_code"].extend([
                    {
                        "type": "file_context",
                        "chunk": chunk
                    }
                    for chunk in file_chunks
                ])
            
            # 分析潜在原因
            if function_name:
                # 搜索相关的错误处理代码
                error_results = self.search_code(f"error handling {function_name}")
                context["potential_causes"].extend([
                    {
                        "type": "error_handling",
                        "description": f"错误处理相关代码",
                        "chunk": result.chunk
                    }
                    for result in error_results[:3]
                ])
                
                # 搜索内存相关代码
                memory_results = self.search_code(f"malloc free {function_name}")
                context["potential_causes"].extend([
                    {
                        "type": "memory_management",
                        "description": f"内存管理相关代码",
                        "chunk": result.chunk
                    }
                    for result in memory_results[:3]
                ])
            
            logger.info(f"崩溃上下文分析完成: {len(context['related_code'])} 个相关代码块")
            
        except Exception as e:
            logger.error(f"分析崩溃上下文失败: {e}")
        
        return context
    
    def get_debugging_suggestions(self, error_description: str) -> List[Dict[str, Any]]:
        """获取调试建议"""
        suggestions = []
        
        try:
            # 基于错误描述搜索相关代码
            search_results = self.search_code(error_description, top_k=10)
            
            for result in search_results:
                suggestion = {
                    "type": "code_analysis",
                    "confidence": result.score,
                    "description": f"在 {result.chunk.file_path} 中发现相关代码",
                    "code_chunk": result.chunk,
                    "suggested_actions": []
                }
                
                # 基于代码内容生成建议
                code_content = result.chunk.content.lower()
                
                if "malloc" in code_content or "calloc" in code_content:
                    suggestion["suggested_actions"].append("检查内存分配是否成功")
                    suggestion["suggested_actions"].append("检查是否有对应的free调用")
                
                if "free" in code_content:
                    suggestion["suggested_actions"].append("检查是否存在双重释放")
                    suggestion["suggested_actions"].append("检查指针是否为NULL")
                
                if "strcpy" in code_content or "strcat" in code_content:
                    suggestion["suggested_actions"].append("检查缓冲区溢出")
                    suggestion["suggested_actions"].append("考虑使用安全的字符串函数")
                
                if "array" in code_content or "[" in code_content:
                    suggestion["suggested_actions"].append("检查数组边界")
                    suggestion["suggested_actions"].append("验证索引值的有效性")
                
                suggestions.append(suggestion)
            
            logger.info(f"生成 {len(suggestions)} 个调试建议")
            
        except Exception as e:
            logger.error(f"生成调试建议失败: {e}")
        
        return suggestions

    def get_all_files(self) -> List[str]:
        """获取所有文件路径"""
        files = set()
        for chunk in self.chunks_cache:
            files.add(chunk.file_path)
        return list(files)

    def get_statistics(self) -> Dict[str, Any]:
        """获取知识库统计信息"""
        stats = {
            "total_chunks": len(self.chunks_cache),
            "total_symbols": len(self.symbols_index),
            "symbol_types": {},
            "file_count": len(set(chunk.file_path for chunk in self.chunks_cache)),
            "languages": set()
        }
        
        # 统计符号类型
        for symbol_list in self.symbols_index.values():
            for symbol in symbol_list:
                if symbol.type not in stats["symbol_types"]:
                    stats["symbol_types"][symbol.type] = 0
                stats["symbol_types"][symbol.type] += 1
        
        # 统计语言类型
        for chunk in self.chunks_cache:
            file_path = Path(chunk.file_path)
            if file_path.suffix.lower() in {'.c', '.h'}:
                stats["languages"].add("C")
            elif file_path.suffix.lower() in {'.cpp', '.cxx', '.cc', '.hpp', '.hxx', '.hh'}:
                stats["languages"].add("C++")
        
        stats["languages"] = list(stats["languages"])

        return stats

    async def analyze_project_with_llm(self) -> Optional[Dict[str, Any]]:
        """使用LLM和工具管理器进行项目分析"""
        try:
            logger.info("开始使用LLM进行项目分析...")

            # 创建带LLM和工具的项目分析器
            tool_manager = ToolManager(None, self)
            llm_client = LLMSession(self.config, tool_manager)
            analyzer = ProjectAnalyzer(self.config, self, llm_client, tool_manager)

            # 检查是否需要重新分析
            if not analyzer.is_analysis_outdated():
                logger.info("项目分析结果仍然有效，加载已有分析")
                return analyzer.load_analysis()

            # 执行项目分析
            analysis_result = await analyzer.analyze_project()

            logger.info("LLM项目分析完成")
            return analysis_result

        except Exception as e:
            logger.error(f"LLM项目分析失败: {e}")
            return None
    def load_analysis(self) -> Optional[Dict[str, Any]]:
        """加载项目分析结果"""
        analyzer = ProjectAnalyzer(self.config, self)
        project_analysis = analyzer.load_analysis()
        if project_analysis:
            return project_analysis
        return self._analyze_project_structure()
    
    def _analyze_project_structure(self) -> Dict[str, Any]:
        """分析项目结构"""
        try:
            # 获取项目基本信息
            project_info = {
                "project_dir": str(self.config.project_code_dir),
                "target_executable": str(self.config.target_executable) if self.config.target_executable else None,
                "target_args": self.config.target_args,
                "main_files": [],
                "header_files": [],
                "source_files": []
            }
            code_filter = self.code_parser.get_code_filter()

            # 搜索主要文件
            main_symbols = self.find_symbol("main")
            if main_symbols:
                project_info["main_files"] = [
                    {"file": symbol.file_path, "line": symbol.line_number}
                    for symbol in main_symbols[:3]
                ]

            # 获取文件统计信息
            all_files = self.get_all_files()
            if all_files:
                project_info["source_files"] = [f for f in all_files if f.endswith(('.c', '.cpp', '.cc'))][:10]
                project_info["header_files"] = [f for f in all_files if f.endswith(('.h', '.hpp'))][:10]

            return project_info

        except Exception as e:
            logger.error(f"项目结构分析失败: {e}")
            return {"error": str(e)}
